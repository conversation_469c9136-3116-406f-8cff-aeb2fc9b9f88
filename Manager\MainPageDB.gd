class_name MainPageDB
extends RefCounted

# Simplified database for main page functionality
class MainPageFoodData:
    var id: int
    var icon_tex_p: String
    var price: int
    
    func _init(food_id: int, icon_path: String, food_price: int):
        id = food_id
        icon_tex_p = icon_path
        price = food_price

# Simple food database
static var food_database = {
    1: MainPageFoodData.new(1, "res://Assets/MainPage/icon_cherry.png", 5),
    2: MainPageFoodData.new(2, "res://Assets/MainPage/icon_cherry.png", 8),
    3: MainPageFoodData.new(3, "res://Assets/MainPage/icon_cherry.png", 10)
}

static func get_food_data(id: int) -> MainPageFoodData:
    if food_database.has(id):
        return food_database[id]
    else:
        # Return default food data if not found
        return food_database[1] 