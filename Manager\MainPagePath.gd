class_name MainPagePath
extends RefCounted

# Main page related UI paths - simplified version for Till project
const PopupWindowP: = "res://Scene/UI/PopupUI/popup_window.tscn"
const UIMsgWindowP: = "res://Scene/UI/Parts/ui_msg_window.tscn"

# Placeholder paths for main page buttons - these would need to be implemented in Till
const UIShopP: = "res://Scene/UI/PopupUI/UIShop/ui_shop.tscn"
const UIFarmP: = "res://Scene/UI/PopupUI/UIFarm/ui_farm.tscn"
const UICollectionP: = "res://Scene/UI/PopupUI/UICollection/ui_collection.tscn"
const UISettingsP: = "res://Scene/UI/PopupUI/UISettings/ui_settings.tscn"
const UIGuideP: = "res://Scene/UI/PopupUI/ui_tutorial.tscn"

# Food picker - simplified for Till
const FoodPickerP: = "res://Scene/Level/food_picker.tscn"

# Cursor paths
const DefaultMouseP: = "res://Assets/UI/cursor.png"
const PoopCleanerMouseP: = "res://Assets/UI/cursor_poop.png" 