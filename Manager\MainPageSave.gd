extends Node

# Simplified save manager for Till project main page
signal farm_changed()

class MainPagePlayerSaveData:
    signal coin_changed()
    
    var coin: int = 1000
    var star_food_id: int = 1
    
    func _init():
        pass

class MainPageSettingsData:
    var always_show_quick_food: bool = false
    
    func _init():
        pass

class MainPageFarmData:
    var applyed_dlc: Array = []
    
    func _init():
        pass

# Player save data mock
var player_save = MainPagePlayerSaveData.new()
var settings = MainPageSettingsData.new()
var current_farm_data = MainPageFarmData.new()

func get_current_farm():
    return current_farm_data

func _ready():
    # Initialize save system
    pass 