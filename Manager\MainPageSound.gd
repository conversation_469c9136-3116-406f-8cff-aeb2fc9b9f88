extends Node

# Simplified sound manager for Till project main page

func bt_hover_audio():
    # TODO: Play button hover sound effect
    print("Button hover sound")

func bt_click_audio():
    # TODO: Play button click sound effect
    print("Button click sound")

func open_ui_audio():
    # TODO: Play UI open sound effect
    print("UI open sound")

func _ready():
    # Initialize audio system
    pass 