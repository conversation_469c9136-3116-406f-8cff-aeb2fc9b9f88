extends Node

# Simplified state manager for Till project main page
signal open_animal_info(animal)
signal open_shop_ui()
signal open_window_adjust_ui()
signal star_food_changed(id)
signal food_picked(id)
signal picked_food_released()
signal toggle_dlc_0(enabled)
signal show_shop_red_pot()

# Current picking item state
var picking_item = null
var is_deleting_deco = false
var is_picking_hp_item = -1

func _ready():
    # Auto-load initialization
    pass 