class_name MainPageUtils
extends RefCounted

# Utility functions for main page
static func trans_number_with_comma(number: int) -> String:
    var str_number = str(number)
    var result = ""
    var count = 0
    
    # Add commas every three digits from right to left
    for i in range(str_number.length() - 1, -1, -1):
        if count > 0 and count % 3 == 0:
            result = "," + result
        result = str_number[i] + result
        count += 1
    
    return result 