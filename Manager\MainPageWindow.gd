extends Node

# Window manager for Till project main page
# Handles window positioning at desktop bottom

var original_window_height: int = 50

func _ready():
    # Initialize window settings when the manager loads
    call_deferred("setup_window_position")

func setup_window_position():
    var window = get_main_window()
    
    # Get current screen information
    var screen = window.current_screen
    var usable_size = DisplayServer.screen_get_usable_rect(screen).size
    var screen_size = DisplayServer.screen_get_size(screen)
    var screen_pos = DisplayServer.screen_get_position(screen)
    
    # Set window size
    var camera_zoom = window.content_scale_factor
    var window_height = original_window_height * camera_zoom
    
    # Set window width to cover full screen width (like Shop project)
    window.size.x = usable_size.x
    window.size.y = window_height
    
    # Position window at bottom of screen (like Shop project)
    var window_pos = Vector2i()
    window_pos.x = 0  # Start from left edge of screen
    window_pos.y = usable_size.y - window_height
    
    # Apply position with screen offset
    window.position = window_pos + Vector2i(screen_pos)
    
    # Set window to always stay on top
    window.always_on_top = true
    
    print("Window positioned at bottom of screen with full width: ", window.position, " size: ", window.size)

func get_main_window() -> Window:
    return get_tree().root 
