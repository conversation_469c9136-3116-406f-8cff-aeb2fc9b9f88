extends CanvasLayer

@onready var bt_shop: TextureButton = $BG / BT_Shop
@onready var bt_facility: TextureButton = $BG / BT_Facility
@onready var bt_collection: TextureButton = $BG / BT_Collection
@onready var bt_settings: TextureButton = $BG / BT_Settings
@onready var bt_guide: TextureButton = $BG / BT_Guide
@onready var bg: TextureRect = $BG
@onready var txt_coin: Label = % TXT_Coin
@onready var tex_quick_food: TextureRect = % TEX_QuickFood
@onready var txt_quick_food_price: Label = % TXT_QuickFoodPrice
@onready var tex_keep_icon: TextureRect = % TEX_KeepIcon
@onready var menu_close_pos: Control = $MenuClosePos
@onready var menu_open_pos: Control = $MenuOpenPos
@onready var tex_shop_red_pot: TextureRect = % TEX_ShopRedPot
@onready var pick_item_tip: Label = % PickItemTip
@onready var pick_bd_tip: Label = % PickBDTip
@onready var delete_mode_tip: Label = % DeleteModeTip
@onready var pick_hp_tip: Label = % PickHPTip
@onready var bt_quick_food: TextureButton = % BT_QuickFood
@onready var coin_box: Control = % CoinBox
@onready var stop_area: Control = % StopArea
@onready var bt_fold: TextureButton = % BT_Fold
@onready var bt_unfold: TextureButton = % BT_Unfold

var ui_animal_info: Control
var ui_shop: Control
var ui_facility: Control
var ui_collection: Control
var ui_settings: Control
var ui_guide: Control

var current_page: Control

var is_hover: = false
var hovered_time: float = 0
var tool_tip_window

func _ready() -> void :
    MainPageState.open_animal_info.connect(_on_open_animal_info)
    MainPageState.open_shop_ui.connect(_on_bt_shop_pressed)
    MainPageState.open_window_adjust_ui.connect(_on_open_window_adjust_ui)
    MainPageState.star_food_changed.connect(_on_star_food_changed)
    MainPageState.food_picked.connect(_on_food_picked)
    MainPageState.picked_food_released.connect(_on_picked_food_released)
    MainPageSave.farm_changed.connect(_on_farm_changed)
    MainPageState.toggle_dlc_0.connect(_on_toggle_dlc_0)
    await get_tree().process_frame
    MainPageState.show_shop_red_pot.connect(_on_show_shop_red_pot)
    
    # Setup main page after all connections are made
    setup()

func _on_farm_changed():
    var dlcs = MainPageSave.get_current_farm().applyed_dlc
    if not dlcs.is_empty():
        for i in dlcs:
            # Simplified DLC check for Till project
            match i:
                0:
                    _on_toggle_dlc_0(true)

func _process(_delta: float) -> void :
    if is_instance_valid(MainPageState.picking_item):
        # Simplified picking item logic
        pick_item_tip.show()
        pick_bd_tip.hide()
    else:
        pick_item_tip.hide()
        pick_bd_tip.hide()

    delete_mode_tip.visible = MainPageState.is_deleting_deco
    pick_hp_tip.visible = MainPageState.is_picking_hp_item != -1

func setup():
    MainPageSave.player_save.coin_changed.connect(_on_coin_changed)
    _on_coin_changed()
    _on_star_food_changed(MainPageSave.player_save.star_food_id)

func _on_bt_mouse_entered(bt_index: int) -> void :
    match bt_index:
        0:
            bt_shop.position.y = 10
            bt_shop.size.y = 18
        1:
            bt_facility.position.y = 29
            bt_facility.size.y = 18
        2:
            bt_collection.position.y = 29
            bt_collection.size.y = 18
        3:
            bt_settings.position.y = 29
            bt_settings.size.y = 18
        4:
            tex_quick_food.position.y = 3
            tex_keep_icon.position.y = 0
        5:
            bt_guide.position.y = 10
            bt_guide.size.y = 18

func _on_bt_mouse_exited(bt_index: int) -> void :
    match bt_index:
        0:
            bt_shop.position.y = 12
            bt_shop.size.y = 16
        1:
            bt_facility.position.y = 31
            bt_facility.size.y = 16
        2:
            bt_collection.position.y = 31
            bt_collection.size.y = 16
        3:
            bt_settings.position.y = 31
            bt_settings.size.y = 16
        4:
            tex_quick_food.position.y = 6
            tex_keep_icon.position.y = 3
        5:
            bt_guide.position.y = 12
            bt_guide.size.y = 16

func _on_bt_shop_pressed() -> void :
    if is_instance_valid(ui_shop):
        ui_shop.get_window().grab_focus()
        tex_shop_red_pot.hide()
        return

    # Create placeholder shop UI
    print("Shop button pressed - TODO: Implement shop UI for Till project")
    tex_shop_red_pot.hide()

func _on_bt_facility_pressed() -> void :
    if is_instance_valid(ui_facility):
        ui_facility.get_window().grab_focus()
        return

    # Create placeholder facility UI
    print("Facility button pressed - TODO: Implement facility UI for Till project")

func _on_open_animal_info(animal):
    print("Animal info requested - TODO: Implement animal info UI for Till project")

func _on_bt_collection_pressed() -> void :
    if is_instance_valid(ui_collection):
        ui_collection.get_window().grab_focus()
        return

    # Create placeholder collection UI
    print("Collection button pressed - TODO: Implement collection UI for Till project")

func _on_bt_settings_pressed() -> void :
    if is_instance_valid(ui_settings):
        ui_settings.get_window().grab_focus()
        return

    # Create placeholder settings UI
    print("Settings button pressed - TODO: Implement settings UI for Till project")

func _on_bt_guide_pressed() -> void :
    if is_instance_valid(ui_guide):
        ui_guide.get_window().grab_focus()
        return

    # Create placeholder guide UI
    print("Guide button pressed - TODO: Implement guide UI for Till project")

func _on_open_window_adjust_ui() -> void :
    if is_instance_valid(current_page):
        current_page.get_window().queue_free()

    print("Window adjust UI requested - TODO: Implement window adjustment for Till project")
    close_menu()

func open_new_window_with(open_page: Control):
    # Simplified window creation for Till project
    var window: = Window.new()
    add_child(window)
    window.add_child(open_page)
    open_ui_sfx()

func _on_bt_fold_pressed() -> void :
    close_menu()

func _on_bt_unfold_pressed() -> void :
    open_menu()

func open_menu():
    bt_unfold.hide()
    bt_fold.show()
    var tween = get_tree().create_tween()
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.set_ease(Tween.EASE_IN_OUT)
    tween.set_parallel(true)
    tween.tween_property(bg, "position", menu_open_pos.position, 0.3)
    tween.tween_property(bt_quick_food, "position", Vector2(-32, 24), 0.3)
    coin_box.show()
    stop_area.position = Vector2(-35, 0)
    stop_area.size = Vector2(120, 50)

func close_menu():
    bt_fold.hide()
    bt_unfold.show()
    var tween = get_tree().create_tween()
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.set_ease(Tween.EASE_IN_OUT)
    tween.set_parallel(true)
    tween.tween_property(bg, "position", menu_close_pos.position, 0.3)
    if not MainPageSave.settings.always_show_quick_food:
        tween.tween_property(bt_quick_food, "position", Vector2(-32, 56), 0.3)
    coin_box.hide()
    stop_area.position = Vector2(-1, 0)
    stop_area.size = Vector2(86, 50)

func _on_bt_quick_food_pressed() -> void :
    if is_instance_valid(MainPageState.picking_item):
        MainPageState.picking_item.queue_free()

    _on_food_picked(MainPageSave.player_save.star_food_id)

func _on_food_picked(id):
    if is_instance_valid(MainPageState.picking_item):
        MainPageState.picking_item.queue_free()

    # Simplified food picker for Till project
    print("Food picked with ID: ", id, " - TODO: Implement food picker for Till project")

func _on_picked_food_released():
    if is_instance_valid(MainPageState.picking_item):
        MainPageState.picking_item.queue_free()
        MainPageState.picking_item = null

func _on_coin_changed():
    var coin = MainPageSave.player_save.coin
    txt_coin.text = MainPageUtils.trans_number_with_comma(coin)

func get_coin_target():
    return $BG / CoinTarget

func bt_hover_sfx():
    MainPageSound.bt_hover_audio()

func bt_click_sfx():
    MainPageSound.bt_click_audio()

func open_ui_sfx():
    MainPageSound.open_ui_audio()

func _on_show_shop_red_pot():
    tex_shop_red_pot.show()

func _on_bt_minimize_pressed() -> void :
    get_window().mode = Window.MODE_MINIMIZED

func _on_star_food_changed(id):
    MainPageSave.player_save.star_food_id = id
    var food_data: = MainPageDB.get_food_data(id)
    tex_quick_food.texture = load(food_data.icon_tex_p)
    txt_quick_food_price.text = str(food_data.price)

func _on_toggle_dlc_0(toggled_on):
    if toggled_on:
        # DLC 0 styling - simplified for Till project
        coin_box.get_theme_stylebox("panel").set("texture", load("res://Assets/UI/MainPage/DLC/main_page_money.png"))
        coin_box.position += Vector2(-8, 0)

        bt_quick_food.texture_normal = load("res://Assets/UI/MainPage/DLC/quickFood_normal.png")
        bt_quick_food.texture_pressed = load("res://Assets/UI/MainPage/DLC/quickFood_pressed.png")
        bt_quick_food.texture_hover = load("res://Assets/UI/MainPage/DLC/quickFood_hover.png")

        bg.texture = load("res://Assets/UI/MainPage/DLC/main_page_bg.png")
    else:
        # Default styling
        coin_box.get_theme_stylebox("panel").set("texture", load("res://Assets/UI/MainPage/main_page_money.png"))
        coin_box.position -= Vector2(-8, 0)

        bt_quick_food.texture_normal = load("res://Assets/UI/MainPage/quickFood_normal.png")
        bt_quick_food.texture_pressed = load("res://Assets/UI/MainPage/quickFood_pressed.png")
        bt_quick_food.texture_hover = load("res://Assets/UI/MainPage/quickFood_hover.png")

        bg.texture = load("res://Assets/UI/MainPage/main_page_bg.png") 