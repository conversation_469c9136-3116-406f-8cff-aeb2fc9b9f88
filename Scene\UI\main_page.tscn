[gd_scene load_steps=31 format=3 uid="uid://dkhv02fatdbju"]

[ext_resource type="Script" uid="uid://b7lwbxubkrfnj" path="res://Scene/UI/main_page.gd" id="1"]
[ext_resource type="Texture2D" uid="uid://bd76rcufa6uk3" path="res://Assets/MainPage/main_page_bg.png" id="2"]
[ext_resource type="Texture2D" uid="uid://bbjrsnwi1r8nw" path="res://Assets/MainPage/main_page_icon_shop.png" id="3"]
[ext_resource type="Texture2D" uid="uid://dpqu2nh7b8rd3" path="res://Assets/MainPage/main_page_icon_shop_pressed.png" id="4"]
[ext_resource type="Texture2D" uid="uid://b14rf7lie1qqw" path="res://Assets/MainPage/spot.png" id="5"]
[ext_resource type="Texture2D" uid="uid://dh7npd3l77uru" path="res://Assets/MainPage/main_page_icon_facility.png" id="6"]
[ext_resource type="Texture2D" uid="uid://cbfsbkkop8wf3" path="res://Assets/MainPage/main_page_icon_facility_pressed.png" id="7"]
[ext_resource type="Texture2D" uid="uid://dc6luhny6ckt" path="res://Assets/MainPage/main_page_icon_handbook.png" id="8"]
[ext_resource type="Texture2D" uid="uid://cwht2y4a8oyv4" path="res://Assets/MainPage/main_page_icon_handbook_pressed.png" id="9"]
[ext_resource type="Texture2D" uid="uid://bm2440ln4ycnl" path="res://Assets/MainPage/main_page_icon_settings.png" id="10"]
[ext_resource type="Texture2D" uid="uid://csg70lai1610q" path="res://Assets/MainPage/main_page_icon_settings_pressed.png" id="11"]
[ext_resource type="Texture2D" uid="uid://bfc10hu8ppve5" path="res://Assets/MainPage/main_page_icon_tutorial.png" id="12"]
[ext_resource type="Texture2D" uid="uid://fu1uixtj8wf4" path="res://Assets/MainPage/main_page_icon_tutorial_pressed.png" id="13"]
[ext_resource type="Texture2D" uid="uid://clx02ge27lkij" path="res://Assets/MainPage/button_fold_normal.png" id="14"]
[ext_resource type="Texture2D" uid="uid://bcm2yttivp487" path="res://Assets/MainPage/button_fold_pressed.png" id="15"]
[ext_resource type="Texture2D" uid="uid://baxb0e7apc05l" path="res://Assets/MainPage/button_fold_hover.png" id="16"]
[ext_resource type="Texture2D" uid="uid://dgaihv3bv50si" path="res://Assets/MainPage/button_unfold_normal.png" id="17"]
[ext_resource type="Texture2D" uid="uid://bbwu1l22lkts4" path="res://Assets/MainPage/button_unfold_pressed.png" id="18"]
[ext_resource type="Texture2D" uid="uid://dwyt53q1sjg08" path="res://Assets/MainPage/button_unfold_hover.png" id="19"]
[ext_resource type="Texture2D" uid="uid://cq44dv2i05ax4" path="res://Assets/MainPage/main_page_money.png" id="20"]
[ext_resource type="Texture2D" uid="uid://bli21dv55anvk" path="res://Assets/MainPage/coin.png" id="21"]
[ext_resource type="Texture2D" uid="uid://d4ibp2oup3u03" path="res://Assets/MainPage/quickFood_normal.png" id="22"]
[ext_resource type="Texture2D" uid="uid://20o7bhhnb73j" path="res://Assets/MainPage/quickFood_pressed.png" id="23"]
[ext_resource type="Texture2D" uid="uid://rrdslvabpya4" path="res://Assets/MainPage/quickFood_hover.png" id="24"]
[ext_resource type="Texture2D" uid="uid://c0d25hagn25g8" path="res://Assets/MainPage/icon_cherry.png" id="25"]
[ext_resource type="Texture2D" uid="uid://cughkaqtcgb6q" path="res://Assets/MainPage/icon_keep.png" id="26"]
[ext_resource type="Texture2D" uid="uid://bx7ylhm81jcl6" path="res://Assets/MainPage/lamp_0_default.png" id="27"]
[ext_resource type="Texture2D" uid="uid://bcuwpg7jar0xw" path="res://Assets/MainPage/lamp_0_pressed.png" id="28"]
[ext_resource type="Texture2D" uid="uid://b05btojsq0y0b" path="res://Assets/MainPage/lamp_0_hover.png" id="29"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_texg7"]
content_margin_left = 6.0
content_margin_top = 8.0
content_margin_right = 21.0
content_margin_bottom = 3.0
texture = ExtResource("20")
texture_margin_left = 10.0
texture_margin_top = 8.0
texture_margin_right = 26.0
texture_margin_bottom = 3.0

[node name="MainPage" type="CanvasLayer"]
script = ExtResource("1")

[node name="MenuClosePos" type="Control" parent="."]
layout_mode = 3
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -20.0
offset_right = -20.0
grow_horizontal = 0

[node name="MenuOpenPos" type="Control" parent="."]
layout_mode = 3
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -85.0
offset_right = -85.0
grow_horizontal = 0

[node name="BG" type="TextureRect" parent="."]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -20.0
offset_right = 65.0
offset_bottom = 50.0
grow_horizontal = 0
mouse_filter = 2
texture = ExtResource("2")

[node name="StopArea" type="Control" parent="BG"]
unique_name_in_owner = true
anchors_preset = 0
offset_left = -1.0
offset_right = 85.0
offset_bottom = 50.0
mouse_force_pass_scroll_events = false

[node name="BT_Shop" type="TextureButton" parent="BG"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -63.0
offset_top = 12.0
offset_right = -28.0
offset_bottom = 28.0
grow_horizontal = 0
texture_normal = ExtResource("3")
texture_pressed = ExtResource("4")

[node name="TEX_ShopRedPot" type="TextureRect" parent="BG/BT_Shop"]
unique_name_in_owner = true
visible = false
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -5.0
offset_top = -2.0
offset_right = 1.0
offset_bottom = 4.0
grow_horizontal = 0
texture = ExtResource("5")
stretch_mode = 3

[node name="BT_Facility" type="TextureButton" parent="BG"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -63.0
offset_top = 31.0
offset_right = -47.0
offset_bottom = 47.0
grow_horizontal = 0
texture_normal = ExtResource("6")
texture_pressed = ExtResource("7")

[node name="BT_Collection" type="TextureButton" parent="BG"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -44.0
offset_top = 31.0
offset_right = -28.0
offset_bottom = 47.0
grow_horizontal = 0
texture_normal = ExtResource("8")
texture_pressed = ExtResource("9")

[node name="BT_Settings" type="TextureButton" parent="BG"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -24.0
offset_top = 31.0
offset_right = -8.0
offset_bottom = 47.0
grow_horizontal = 0
texture_normal = ExtResource("10")
texture_pressed = ExtResource("11")

[node name="BT_Guide" type="TextureButton" parent="BG"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -25.0
offset_top = 12.0
offset_right = -9.0
offset_bottom = 28.0
grow_horizontal = 0
texture_normal = ExtResource("12")
texture_pressed = ExtResource("13")

[node name="BT_Fold" type="TextureButton" parent="BG"]
unique_name_in_owner = true
visible = false
layout_mode = 0
offset_left = 3.0
offset_top = 2.0
offset_right = 20.0
offset_bottom = 50.0
texture_normal = ExtResource("14")
texture_pressed = ExtResource("15")
texture_hover = ExtResource("16")
stretch_mode = 3

[node name="BT_Unfold" type="TextureButton" parent="BG"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 3.0
offset_top = 2.0
offset_right = 20.0
offset_bottom = 50.0
texture_normal = ExtResource("17")
texture_pressed = ExtResource("18")
texture_hover = ExtResource("19")
stretch_mode = 3

[node name="CoinBox" type="PanelContainer" parent="BG"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -126.0
offset_top = 4.0
offset_right = -69.0
offset_bottom = 24.0
grow_horizontal = 0
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_texg7")

[node name="HBoxContainer" type="HBoxContainer" parent="BG/CoinBox"]
layout_mode = 2
mouse_filter = 2
theme_override_constants/separation = 5

[node name="TextureRect" type="TextureRect" parent="BG/CoinBox/HBoxContainer"]
layout_mode = 2
mouse_filter = 2
texture = ExtResource("21")
stretch_mode = 3

[node name="TXT_Coin" type="Label" parent="BG/CoinBox/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 8
theme_override_font_sizes/font_size = 8
text = "600"
horizontal_alignment = 2
vertical_alignment = 2

[node name="BT_QuickFood" type="TextureButton" parent="BG"]
unique_name_in_owner = true
layout_mode = 0
offset_left = -32.0
offset_top = 56.0
offset_right = -2.0
offset_bottom = 82.0
texture_normal = ExtResource("22")
texture_pressed = ExtResource("23")
texture_hover = ExtResource("24")

[node name="TEX_QuickFood" type="TextureRect" parent="BG/BT_QuickFood"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 10.0
offset_top = 6.0
offset_right = 20.0
offset_bottom = 16.0
texture = ExtResource("25")
stretch_mode = 5

[node name="TEX_KeepIcon" type="TextureRect" parent="BG/BT_QuickFood"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 7.0
offset_top = 3.0
offset_right = 12.0
offset_bottom = 9.0
texture = ExtResource("26")
stretch_mode = 2

[node name="CenterContainer" type="CenterContainer" parent="BG/BT_QuickFood"]
layout_mode = 0
offset_left = 2.0
offset_top = 20.0
offset_right = 28.0
offset_bottom = 26.0

[node name="HBoxContainer" type="HBoxContainer" parent="BG/BT_QuickFood/CenterContainer"]
layout_mode = 2
theme_override_constants/separation = 1
metadata/_edit_group_ = true

[node name="TXT_QuickFoodPrice" type="Label" parent="BG/BT_QuickFood/CenterContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 8
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 5
text = "5"
horizontal_alignment = 1
vertical_alignment = 2

[node name="TextureRect" type="TextureRect" parent="BG/BT_QuickFood/CenterContainer/HBoxContainer"]
custom_minimum_size = Vector2(4, 4)
layout_mode = 2
size_flags_horizontal = 0
size_flags_vertical = 0
texture = ExtResource("21")
expand_mode = 1
stretch_mode = 5

[node name="CoinTarget" type="Control" parent="BG"]
anchors_preset = 0
offset_left = -29.0
offset_top = 17.0
offset_right = -29.0
offset_bottom = 17.0

[node name="BT_Minimize" type="TextureButton" parent="."]
custom_minimum_size = Vector2(15, 0)
offset_right = 26.0
offset_bottom = 30.0
texture_normal = ExtResource("27")
texture_pressed = ExtResource("28")
texture_hover = ExtResource("29")

[node name="PickItemTip" type="Label" parent="."]
unique_name_in_owner = true
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -32.5
offset_top = 2.0
offset_right = 32.5
offset_bottom = 13.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.775063, 0.775063, 0.775063, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 8
text = "PICKING_ITEM_TIP"

[node name="PickBDTip" type="Label" parent="."]
unique_name_in_owner = true
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -32.5
offset_top = 2.0
offset_right = 32.5
offset_bottom = 13.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.775063, 0.775063, 0.775063, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 8
text = "PICKING_BD_TIP"

[node name="DeleteModeTip" type="Label" parent="."]
unique_name_in_owner = true
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -33.0
offset_top = 2.0
offset_right = 33.0
offset_bottom = 13.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.775063, 0.775063, 0.775063, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 8
text = "DELETE_MODE_TIP"

[node name="PickHPTip" type="Label" parent="."]
unique_name_in_owner = true
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -33.0
offset_top = 2.0
offset_right = 33.0
offset_bottom = 13.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.775063, 0.775063, 0.775063, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 8
text = "PICKING_ITEM_TIP"

[connection signal="mouse_entered" from="BG/BT_Shop" to="." method="bt_hover_sfx"]
[connection signal="mouse_entered" from="BG/BT_Shop" to="." method="_on_bt_mouse_entered" binds= [0]]
[connection signal="mouse_exited" from="BG/BT_Shop" to="." method="_on_bt_mouse_exited" binds= [0]]
[connection signal="pressed" from="BG/BT_Shop" to="." method="_on_bt_shop_pressed"]
[connection signal="pressed" from="BG/BT_Shop" to="." method="bt_click_sfx"]
[connection signal="mouse_entered" from="BG/BT_Facility" to="." method="bt_hover_sfx"]
[connection signal="mouse_entered" from="BG/BT_Facility" to="." method="_on_bt_mouse_entered" binds= [1]]
[connection signal="mouse_exited" from="BG/BT_Facility" to="." method="_on_bt_mouse_exited" binds= [1]]
[connection signal="pressed" from="BG/BT_Facility" to="." method="_on_bt_facility_pressed"]
[connection signal="pressed" from="BG/BT_Facility" to="." method="bt_click_sfx"]
[connection signal="mouse_entered" from="BG/BT_Collection" to="." method="bt_hover_sfx"]
[connection signal="mouse_entered" from="BG/BT_Collection" to="." method="_on_bt_mouse_entered" binds= [2]]
[connection signal="mouse_exited" from="BG/BT_Collection" to="." method="_on_bt_mouse_exited" binds= [2]]
[connection signal="pressed" from="BG/BT_Collection" to="." method="_on_bt_collection_pressed"]
[connection signal="pressed" from="BG/BT_Collection" to="." method="bt_click_sfx"]
[connection signal="mouse_entered" from="BG/BT_Settings" to="." method="bt_hover_sfx"]
[connection signal="mouse_entered" from="BG/BT_Settings" to="." method="_on_bt_mouse_entered" binds= [3]]
[connection signal="mouse_exited" from="BG/BT_Settings" to="." method="_on_bt_mouse_exited" binds= [3]]
[connection signal="pressed" from="BG/BT_Settings" to="." method="_on_bt_settings_pressed"]
[connection signal="pressed" from="BG/BT_Settings" to="." method="bt_click_sfx"]
[connection signal="mouse_entered" from="BG/BT_Guide" to="." method="bt_hover_sfx"]
[connection signal="mouse_entered" from="BG/BT_Guide" to="." method="_on_bt_mouse_entered" binds= [5]]
[connection signal="mouse_exited" from="BG/BT_Guide" to="." method="_on_bt_mouse_exited" binds= [5]]
[connection signal="pressed" from="BG/BT_Guide" to="." method="_on_bt_guide_pressed"]
[connection signal="pressed" from="BG/BT_Guide" to="." method="bt_click_sfx"]
[connection signal="mouse_entered" from="BG/BT_Fold" to="." method="bt_hover_sfx"]
[connection signal="pressed" from="BG/BT_Fold" to="." method="_on_bt_fold_pressed"]
[connection signal="pressed" from="BG/BT_Fold" to="." method="bt_click_sfx"]
[connection signal="mouse_entered" from="BG/BT_Unfold" to="." method="bt_hover_sfx"]
[connection signal="pressed" from="BG/BT_Unfold" to="." method="bt_click_sfx"]
[connection signal="pressed" from="BG/BT_Unfold" to="." method="_on_bt_unfold_pressed"]
[connection signal="mouse_entered" from="BG/BT_QuickFood" to="." method="bt_hover_sfx"]
[connection signal="mouse_entered" from="BG/BT_QuickFood" to="." method="_on_bt_mouse_entered" binds= [4]]
[connection signal="mouse_exited" from="BG/BT_QuickFood" to="." method="_on_bt_mouse_exited" binds= [4]]
[connection signal="pressed" from="BG/BT_QuickFood" to="." method="_on_bt_quick_food_pressed"]
[connection signal="pressed" from="BG/BT_QuickFood" to="." method="bt_click_sfx"]
[connection signal="pressed" from="BT_Minimize" to="." method="_on_bt_minimize_pressed"]
