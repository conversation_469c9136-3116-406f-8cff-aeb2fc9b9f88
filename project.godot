; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Till"
run/main_scene="res://Scene/main_scene.tscn"
config/features=PackedStringArray("4.4", "GL Compatibility")
boot_splash/fullsize=false
boot_splash/image="res://logo.png"
config/icon="res://logo.png"

[autoload]

MainPageState="*res://Manager/MainPageState.gd"
MainPageSave="*res://Manager/MainPageSave.gd"
MainPageSound="*res://Manager/MainPageSound.gd"
MainPageWindow="*res://Manager/MainPageWindow.gd"

[display]

window/size/viewport_width=1920
window/size/viewport_height=50
window/size/resizable=false
window/size/borderless=true
window/size/transparent=true
window/subwindows/embed_subwindows=false
window/stretch/scale=2.0
window/per_pixel_transparency/allowed=true
mouse_cursor/custom_image="res://Assets/MainPage/cursor.png"

[rendering]

renderer/rendering_method="gl_compatibility"
renderer/rendering_method.mobile="gl_compatibility"
viewport/transparent_background=true
